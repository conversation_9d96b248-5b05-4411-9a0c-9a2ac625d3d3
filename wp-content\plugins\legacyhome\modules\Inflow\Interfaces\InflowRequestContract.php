<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Interfaces;

use WP_Error;

interface InflowRequestContract
{
	/**
	 * Perform a request to the Inflow API.
	 *
	 * @param string $method  HTTP verb.
	 * @param string $resource API resource name (e.g., 'customers').
	 * @param int|string|null $id Optional resource ID.
	 * @param array $query Optional query parameters.
	 * @param array $body Optional request payload.
	 *
	 * @return array|WP_Error Associative array on success or WP_Error on failure.
	 */
	public function request(
		string $method,
		string $resource,
		int|string|null $id = null,
		array $query = [],
		array $body = []
	): array|WP_Error;
}
