<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow;

use Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract;
use Contactjavas\Legacyhome\Module\Inflow\Traits\{
	VendorTrait,
	CustomerTrait,
	ProductTrait,
	SalesOrderTrait,
	PurchaseOrderTrait,
};
use WP_Error;

/**
 * @implements InflowRequestContract
 */
class InflowApiClient implements InflowRequestContract
{
	use CustomerTrait;
	use VendorTrait;
	use ProductTrait;
	use SalesOrderTrait;
	use PurchaseOrderTrait;

	protected string $baseUrl;

	protected array $headers;

	protected int $maxRetries = 3;

	public function __construct( InflowAuthClient $auth )
	{
		$this->baseUrl = rtrim( $auth->getBaseUrl(), '/' );
		$this->headers = $auth->getHttpHeaders();
	}

	public function request(
		string $method,
		string $resource,
		int|string|null $id = null,
		array $query = [],
		array $body = []
	): array|WP_Error {
		$method = strtoupper( $method );
		$url    = $this->baseUrl . '/' . ltrim( $resource, '/' );

		if ( $id !== null ) {
			$url .= '/' . urlencode( (string)$id );
		}

		if ( $method === 'GET' && $query ) {
			$url = add_query_arg( $query, $url );
		}

		$args = [
			'method'  => $method,
			'headers' => $this->headers,
			'timeout' => 15,
		];

		if ( in_array( $method, ['POST', 'PUT', 'PATCH', 'DELETE'], true ) && $body ) {
			$args['body'] = wp_json_encode( $body );
		}

		for ( $attempt = 1; $attempt <= $this->maxRetries; $attempt++ ) {
			$response = wp_remote_request( $url, $args );

			if ( is_wp_error( $response ) ) {
				if ( $attempt === $this->maxRetries ) {
					return $response;
				}
				sleep( pow( 2, $attempt ) );
				continue;
			}

			$code     = wp_remote_retrieve_response_code( $response );
			$bodyText = wp_remote_retrieve_body( $response );
			$parsed   = json_decode( $bodyText, true );

			if ( $code >= 500 ) {
				if ( $attempt === $this->maxRetries ) {
					return new WP_Error(
						'inflow_server_error',
						"Server error after {$attempt} attempts.",
						['status' => $code, 'body' => $parsed ?? $bodyText]
					);
				}
				sleep( pow( 2, $attempt ) );
				continue;
			}

			if ( $code >= 400 ) {
				return new WP_Error(
					'inflow_api_error',
					$parsed['message'] ?? "API error {$code}",
					['status' => $code, 'body' => $parsed ?? $bodyText]
				);
			}

			return is_array( $parsed ) ? $parsed : [];
		}

		return new WP_Error( 'inflow_unknown_error', 'Unknown failure during API request.' );
	}
}
