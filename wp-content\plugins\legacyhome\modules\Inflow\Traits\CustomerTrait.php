<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait CustomerTrait
{
	protected function getCustomerResource(): string
	{
		return 'customers';
	}

	/**
	 * @param array $options e.g. ['filter' => [...], 'include' => '...', 'count' => 50, 'sort' => 'name']
	 * @return array|WP_Error
	 */
	public function listCustomers( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getCustomerResource(), null, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function getCustomer( int|string $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getCustomerResource(), $id, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function createCustomer( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getCustomerResource(), null, [], $data );
	}

	/**
	 * @return array|WP_Error
	 */
	public function updateCustomer( int|string $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getCustomerResource(), $id, [], $data );
	}

	/**
	 * @return bool|WP_Error
	 */
	public function deleteCustomer( int|string $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getCustomerResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
